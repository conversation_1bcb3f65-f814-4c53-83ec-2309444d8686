<template>
  <div class="modal fade lumi-fade" tabindex="-1" id="modalRegistrarHistorico">
    <div class="modal-dialog modal-dialog-centered modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Registrar Histórico</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            ref="closeModalRegistrarHistorico"
          ></button>
        </div>
        <div class="modal-body px-4">
          <form @submit.prevent="salvarHistorico">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="dataHistorico" class="form-label">Data</label>
                <input
                  type="date"
                  class="form-control"
                  id="dataHistorico"
                  v-model="historico.data"
                  required
                />
              </div>
              <div class="col-md-6 mb-3">
                <label for="horarioHistorico" class="form-label">Ho<PERSON><PERSON><PERSON></label>
                <input
                  type="time"
                  class="form-control"
                  id="horarioHistorico"
                  v-model="historico.horario"
                  required
                />
              </div>
            </div>
            
            <div class="mb-3">
              <label for="descricaoHistorico" class="form-label">Descrição do Histórico</label>
              <textarea
                class="form-control"
                id="descricaoHistorico"
                v-model="historico.descricao"
                rows="6"
                placeholder="Descreva o que aconteceu nesta data..."
                required
              ></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button 
            type="button" 
            class="btn btn-primary" 
            @click="salvarHistorico"
            :disabled="isLoading || !isFormValido"
          >
            <span v-if="isLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Salvar Histórico
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import { criarHistoricoPaciente } from "@/services/historicoPacienteService";
import cSwal from "@/utils/cSwal.js";
import { openModal, closeModal } from "@/utils/modalHelper.js";

export default {
  name: "RegistrarHistoricoModal",
  emits: ['historico-salvo'],
  data() {
    return {
      isLoading: false,
      pacienteId: null,
      historico: {
        data: moment().format('YYYY-MM-DD'),
        horario: moment().format('HH:mm'),
        descricao: ''
      }
    };
  },
  computed: {
    isFormValido() {
      return this.historico.data && 
             this.historico.horario && 
             this.historico.descricao.trim().length > 0;
    }
  },
  methods: {
    abrirModal(pacienteId) {
      this.pacienteId = pacienteId;
      this.resetForm();
      openModal('modalRegistrarHistorico');
    },
    resetForm() {
      this.historico = {
        data: moment().format('YYYY-MM-DD'),
        horario: moment().format('HH:mm'),
        descricao: ''
      };
    },
    async salvarHistorico() {
      if (!this.isFormValido) {
        cSwal.cAlert("Por favor, preencha todos os campos obrigatórios.");
        return;
      }

      this.isLoading = true;

      try {
        const historicoData = {
          paciente_id: this.pacienteId,
          data: this.historico.data,
          horario: this.historico.horario + ':00', // Adicionar segundos
          codigo_acao: 'registro_manual',
          descricao: this.historico.descricao,
          referente_tratamento: false
        };

        const response = await criarHistoricoPaciente(historicoData);
        
        if (response) {
          cSwal.cSuccess("Histórico registrado com sucesso!");
          this.$emit('historico-salvo', response);
          closeModal('modalRegistrarHistorico');
          this.resetForm();
        }
      } catch (error) {
        console.error("Erro ao salvar histórico:", error);
        cSwal.cError("Erro ao salvar o histórico. Por favor, tente novamente.");
      } finally {
        this.isLoading = false;
      }
    }
  }
};
</script>

<style scoped>
/* Animação de fade para o modal */
.lumi-fade.modal-closing .modal-dialog {
  transform: translate(0, -25px);
  transition: transform 0.3s ease-out;
}

.lumi-fade.modal-closing {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}
</style>
